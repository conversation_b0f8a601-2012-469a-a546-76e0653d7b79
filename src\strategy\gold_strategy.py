"""
Gold Trading Strategy Engine
Combines MACD, ATR, Pivot Points with AI/ML predictions for trading decisions
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime
import MetaTrader5 as mt5

from ..indicators.technical_indicators import TechnicalIndicators, MACDSignal, ATRData, PivotPoints
# from ..models.dqn_agent import DQNAgent  # Temporarily disabled for Python 3.13 compatibility
from ..risk.risk_manager import RiskManager, PositionSize
from ..core.mt5_client import MT5Client, PositionInfo
from ..utils.logger import get_logger, TradingLogger
from ..utils.config import BotConfig

logger = get_logger(__name__)
trading_logger = TradingLogger("strategy")


@dataclass
class TradingSignal:
    """Trading signal data structure"""
    action: str  # 'buy', 'sell', 'hold', 'close'
    strength: float  # Signal strength 0-1
    entry_price: float
    stop_loss: float
    take_profit: float
    volume: float
    confidence: float  # AI confidence 0-1
    reasoning: str  # Human-readable reasoning
    timestamp: datetime


@dataclass
class MarketState:
    """Current market state representation"""
    price: float
    bid: float
    ask: float
    spread: float
    macd_signal: MACDSignal
    atr_data: ATRData
    pivot_points: PivotPoints
    ai_prediction: Optional[int]
    ai_confidence: float
    market_trend: str  # 'bullish', 'bearish', 'sideways'
    volatility: str  # 'low', 'medium', 'high'


class GoldTradingStrategy:
    """Advanced Gold Trading Strategy with AI Integration"""

    def __init__(self, config: BotConfig, mt5_client: MT5Client):
        self.config = config
        self.mt5_client = mt5_client

        # Initialize components
        self.indicators = TechnicalIndicators(config)
        self.risk_manager = RiskManager(config.risk, config.trading)

        # Initialize AI agent if enabled (temporarily disabled for Python 3.13)
        self.ai_agent = None
        # if config.enable_ai:
        #     state_size = config.ai.state_size  # Feature vector size
        #     action_size = config.ai.action_size  # Hold, Buy, Sell
        #     self.ai_agent = DQNAgent(state_size, action_size, config.ai)
        #     self._load_ai_model()

        # Strategy parameters
        self.min_signal_strength = config.strategy_scoring.min_signal_strength
        self.min_ai_confidence = config.strategy_scoring.min_ai_confidence
        self.lookback_period = config.indicators.lookback_period

        # State tracking
        self.last_signal = None
        self.market_data_cache = None
        self.feature_cache = None

        logger.info("Gold Trading Strategy initialized")

    def analyze_market(self, timeframe: int = mt5.TIMEFRAME_M5) -> MarketState:
        """
        Analyze current market conditions

        Args:
            timeframe: MT5 timeframe constant

        Returns:
            MarketState object with current analysis
        """
        # Get market data
        data = self.mt5_client.get_historical_data(timeframe, self.lookback_period)
        if data is None or len(data) < 50:
            logger.error("Insufficient market data for analysis")
            return self._create_empty_market_state()

        self.market_data_cache = data

        # Get current prices
        bid, ask = self.mt5_client.get_current_price()
        if bid is None or ask is None:
            logger.error("Failed to get current prices")
            return self._create_empty_market_state()

        current_price = (bid + ask) / 2
        spread = ask - bid

        # Calculate technical indicators
        macd_signal = self.indicators.calculate_macd(data)
        atr_data = self.indicators.calculate_atr(data)
        pivot_points = self.indicators.calculate_pivot_points(data)

        # Get AI prediction if enabled
        ai_prediction = None
        ai_confidence = 0.0
        if self.ai_agent and self.config.enable_ai:
            features = self._extract_features(data)
            if features is not None:
                ai_prediction = self.ai_agent.get_action(features, training=False)
                q_values = self.ai_agent.get_q_values(features)
                ai_confidence = np.max(q_values) / (np.sum(np.abs(q_values)) + 1e-8)

        # Determine market trend and volatility
        market_trend = self._determine_market_trend(data, macd_signal, pivot_points)
        volatility = atr_data.volatility_level

        return MarketState(
            price=current_price,
            bid=bid,
            ask=ask,
            spread=spread,
            macd_signal=macd_signal,
            atr_data=atr_data,
            pivot_points=pivot_points,
            ai_prediction=ai_prediction,
            ai_confidence=ai_confidence,
            market_trend=market_trend,
            volatility=volatility
        )

    def generate_signal(self, market_state: MarketState,
                       current_positions: List[PositionInfo],
                       account_info: Dict) -> Optional[TradingSignal]:
        """
        Generate trading signal based on market analysis

        Args:
            market_state: Current market state
            current_positions: List of open positions
            account_info: Account information

        Returns:
            TradingSignal if signal generated, None otherwise
        """
        # Log market analysis regardless of signal generation
        self._log_market_analysis(market_state, current_positions, account_info)

        # Check if trading is allowed
        trading_allowed, reason = self.risk_manager.check_trading_allowed(
            current_positions, account_info
        )

        if not trading_allowed:
            logger.info(f"Trading not allowed: {reason}")
            # Log rejected signal due to trading restrictions
            self._log_rejected_signal("TRADING_RESTRICTED", reason, market_state)
            return None

        # Check spread conditions
        symbol_info = self.mt5_client.get_symbol_info()
        if symbol_info and not self.risk_manager.check_spread_condition(symbol_info):
            logger.info(f"Spread too high: {market_state.spread}")
            # Log rejected signal due to high spread
            self._log_rejected_signal("HIGH_SPREAD", f"Spread: {market_state.spread:.5f}", market_state)
            return None

        # Generate signal based on strategy rules
        signal = self._evaluate_trading_conditions(market_state, current_positions)

        if signal is None:
            # Log no signal generated (strength too low)
            self._log_no_signal(market_state, current_positions)
            return None

                # Calculate position sizing
        if symbol_info:
            trading_logger.log_warning("POSITION_SIZING", f"Using symbol_info: {symbol_info}")
            position_size = self.risk_manager.calculate_position_size(
                account_info['balance'],
                signal.entry_price,
                signal.stop_loss,
                market_state.atr_data.atr,
                symbol_info
            )
        else:
            # Fallback position sizing if symbol_info is None
            trading_logger.log_warning("POSITION_SIZING", "Symbol info is None, using fallback")
            position_size = self.risk_manager.calculate_position_size(
                account_info['balance'],
                signal.entry_price,
                signal.stop_loss,
                market_state.atr_data.atr,
                {}
            )

        trading_logger.log_warning("POSITION_SIZING", f"Calculated volume: {position_size.volume}, Risk amount: {position_size.risk_amount}")

        # Update signal with calculated volume
        signal.volume = position_size.volume
        signal.stop_loss = position_size.stop_loss
        signal.take_profit = position_size.take_profit

        # Log successful signal generation
        self._log_successful_signal(signal, market_state, position_size)

        self.last_signal = signal
        return signal

    def _log_market_analysis(self, market_state: MarketState,
                           current_positions: List[PositionInfo],
                           account_info: Dict):
        """Log comprehensive market analysis"""
        analysis_data = {
            'price': market_state.price,
            'bid': market_state.bid,
            'ask': market_state.ask,
            'spread': market_state.spread,
            'macd_trend': market_state.macd_signal.trend,
            'macd_crossover': market_state.macd_signal.crossover,
            'pivot_level': market_state.pivot_points.current_level,
            'market_trend': market_state.market_trend,
            'volatility': market_state.volatility,
            'atr': market_state.atr_data.atr,
            'ai_prediction': market_state.ai_prediction,
            'ai_confidence': market_state.ai_confidence,
            'open_positions': len(current_positions),
            'account_balance': account_info.get('balance', 0),
            'account_equity': account_info.get('equity', 0)
        }

        trading_logger.log_signal(
            "MARKET_ANALYSIS",
            0.0,  # No signal strength for analysis
            analysis_data,
            analysis_type="comprehensive"
        )

    def _log_rejected_signal(self, rejection_reason: str, details: str,
                           market_state: MarketState):
        """Log rejected signal with reason"""
        rejection_data = {
            'rejection_reason': rejection_reason,
            'details': details,
            'price': market_state.price,
            'spread': market_state.spread,
            'macd_trend': market_state.macd_signal.trend,
            'pivot_level': market_state.pivot_points.current_level,
            'market_trend': market_state.market_trend
        }

        trading_logger.log_signal(
            "SIGNAL_REJECTED",
            0.0,
            rejection_data,
            rejection_type=rejection_reason
        )

    def _log_no_signal(self, market_state: MarketState,
                      current_positions: List[PositionInfo]):
        """Log when no signal is generated (strength too low)"""
        no_signal_data = {
            'reason': 'STRENGTH_TOO_LOW',
            'price': market_state.price,
            'macd_trend': market_state.macd_signal.trend,
            'pivot_level': market_state.pivot_points.current_level,
            'market_trend': market_state.market_trend,
            'volatility': market_state.volatility,
            'open_positions': len(current_positions)
        }

        trading_logger.log_signal(
            "NO_SIGNAL",
            0.0,
            no_signal_data,
            no_signal_reason="insufficient_strength"
        )

    def _log_successful_signal(self, signal: TradingSignal,
                             market_state: MarketState,
                             position_size):
        """Log successful signal generation"""
        signal_data = {
            'action': signal.action,
            'strength': signal.strength,
            'entry_price': signal.entry_price,
            'stop_loss': signal.stop_loss,
            'take_profit': signal.take_profit,
            'volume': signal.volume,
            'confidence': signal.confidence,
            'macd_trend': market_state.macd_signal.trend,
            'pivot_level': market_state.pivot_points.current_level,
            'ai_prediction': market_state.ai_prediction,
            'ai_confidence': market_state.ai_confidence,
            'market_trend': market_state.market_trend,
            'volatility': market_state.volatility,
            'atr': market_state.atr_data.atr,
            'risk_amount': position_size.risk_amount,
            'risk_reward_ratio': position_size.risk_reward_ratio
        }

        trading_logger.log_signal(
            signal.action.upper(),
            signal.strength,
            signal_data,
            reasoning=signal.reasoning
        )

    def _evaluate_trading_conditions(self, market_state: MarketState,
                                   current_positions: List[PositionInfo]) -> Optional[TradingSignal]:
        """Evaluate trading conditions and generate signal"""

        # Check for position management first
        if current_positions:
            close_signal = self._check_position_management(market_state, current_positions)
            if close_signal:
                return close_signal

        # Don't open new positions if we already have maximum positions
        if len(current_positions) >= self.config.trading.max_positions:
            return None

        # Calculate signal components
        technical_score = self._calculate_technical_score(market_state)
        ai_score = self._calculate_ai_score(market_state)

        # Combine scores
        combined_score = self._combine_scores(technical_score, ai_score, market_state)

        # Generate signal based on combined score
        if combined_score['buy_strength'] > self.min_signal_strength:
            return self._create_buy_signal(market_state, combined_score)
        elif combined_score['sell_strength'] > self.min_signal_strength:
            return self._create_sell_signal(market_state, combined_score)

        return None

    def _calculate_technical_score(self, market_state: MarketState) -> Dict:
        """Calculate technical analysis score"""
        score = {'buy': 0, 'sell': 0, 'factors': []}

        # MACD analysis
        if market_state.macd_signal.crossover == 'bullish_cross':
            score['buy'] += self.config.strategy_scoring.macd_crossover_bullish
            score['factors'].append('MACD bullish crossover')
        elif market_state.macd_signal.crossover == 'bearish_cross':
            score['sell'] += self.config.strategy_scoring.macd_crossover_bearish
            score['factors'].append('MACD bearish crossover')
        elif market_state.macd_signal.trend == 'bullish':
            score['buy'] += self.config.strategy_scoring.macd_trend_bullish
            score['factors'].append('MACD bullish trend')
        elif market_state.macd_signal.trend == 'bearish':
            score['sell'] += self.config.strategy_scoring.macd_trend_bearish
            score['factors'].append('MACD bearish trend')

        # Pivot Points analysis
        if market_state.pivot_points.current_level == 'above_pivot':
            score['buy'] += self.config.strategy_scoring.pivot_above
            score['factors'].append('Price above pivot')
        elif market_state.pivot_points.current_level == 'below_pivot':
            score['sell'] += self.config.strategy_scoring.pivot_below
            score['factors'].append('Price below pivot')

        # Market trend analysis
        if market_state.market_trend == 'bullish':
            score['buy'] += self.config.strategy_scoring.market_trend_bullish
            score['factors'].append('Bullish market trend')
        elif market_state.market_trend == 'bearish':
            score['sell'] += self.config.strategy_scoring.market_trend_bearish
            score['factors'].append('Bearish market trend')

        # Enhanced volatility filter
        if market_state.volatility == 'high':
            volatility_multiplier = getattr(self.config.strategy_scoring, 'volatility_filter', 0.6)
            score['buy'] *= volatility_multiplier  # Reduce signal strength in high volatility
            score['sell'] *= volatility_multiplier
            score['factors'].append(f'High volatility - reduced confidence ({volatility_multiplier}x)')
        elif market_state.volatility == 'low':
            # Slightly boost signals in low volatility (stable conditions)
            score['buy'] *= 1.1
            score['sell'] *= 1.1
            score['factors'].append('Low volatility - increased confidence')

        # Advanced trend filter - only trade with the trend
        if not self._check_trend_alignment(market_state):
            score['buy'] *= 0.3  # Heavily reduce counter-trend signals
            score['sell'] *= 0.3
            score['factors'].append('Counter-trend signal - heavily reduced')

        # Time-based filter - avoid high volatility hours
        if self._is_high_volatility_time():
            score['buy'] *= 0.5
            score['sell'] *= 0.5
            score['factors'].append('High volatility time - reduced confidence')

        # Confirmation filter - require multiple confirmations
        if not self._has_sufficient_confirmations(market_state):
            score['buy'] *= 0.4
            score['sell'] *= 0.4
            score['factors'].append('Insufficient confirmations - reduced confidence')

        return score

    def _calculate_ai_score(self, market_state: MarketState) -> Dict:
        """Calculate AI model score"""
        score = {'buy': 0, 'sell': 0, 'factors': []}

        if not self.config.enable_ai or market_state.ai_prediction is None:
            return score

        # AI prediction: 0=Hold, 1=Buy, 2=Sell
        if market_state.ai_prediction == 1 and market_state.ai_confidence > self.min_ai_confidence:
            score['buy'] += self.config.strategy_scoring.ai_prediction_weight * market_state.ai_confidence
            score['factors'].append(f'AI buy prediction (confidence: {market_state.ai_confidence:.2f})')
        elif market_state.ai_prediction == 2 and market_state.ai_confidence > self.min_ai_confidence:
            score['sell'] += self.config.strategy_scoring.ai_prediction_weight * market_state.ai_confidence
            score['factors'].append(f'AI sell prediction (confidence: {market_state.ai_confidence:.2f})')

        return score

    def _combine_scores(self, technical_score: Dict, ai_score: Dict,
                       market_state: MarketState) -> Dict:
        """Combine technical and AI scores"""
        # Weight factors
        technical_weight = self.config.strategy_scoring.technical_weight if self.config.enable_ai else 1.0
        ai_weight = self.config.strategy_scoring.ai_weight if self.config.enable_ai else 0.0

        buy_strength = (technical_score['buy'] * technical_weight +
                       ai_score['buy'] * ai_weight)
        sell_strength = (technical_score['sell'] * technical_weight +
                        ai_score['sell'] * ai_weight)

        # Combine factors
        all_factors = technical_score['factors'] + ai_score['factors']

        return {
            'buy_strength': buy_strength,
            'sell_strength': sell_strength,
            'factors': all_factors
        }

    def _create_buy_signal(self, market_state: MarketState,
                          combined_score: Dict) -> TradingSignal:
        """Create buy signal"""
        entry_price = market_state.ask
        atr = market_state.atr_data.atr

        # Calculate stop loss and take profit
        stop_loss = entry_price - (atr * self.config.risk.stop_loss_atr_multiplier)
        take_profit = entry_price + (atr * self.config.risk.take_profit_atr_multiplier)

        reasoning = "BUY: " + " | ".join(combined_score['factors'])

        return TradingSignal(
            action='buy',
            strength=combined_score['buy_strength'],
            entry_price=entry_price,
            stop_loss=stop_loss,
            take_profit=take_profit,
            volume=self.config.trading.default_volume,  # Will be updated by risk manager
            confidence=market_state.ai_confidence,
            reasoning=reasoning,
            timestamp=datetime.now()
        )

    def _create_sell_signal(self, market_state: MarketState,
                           combined_score: Dict) -> TradingSignal:
        """Create sell signal"""
        entry_price = market_state.bid
        atr = market_state.atr_data.atr

        # Calculate stop loss and take profit
        stop_loss = entry_price + (atr * self.config.risk.stop_loss_atr_multiplier)
        take_profit = entry_price - (atr * self.config.risk.take_profit_atr_multiplier)

        reasoning = "SELL: " + " | ".join(combined_score['factors'])

        return TradingSignal(
            action='sell',
            strength=combined_score['sell_strength'],
            entry_price=entry_price,
            stop_loss=stop_loss,
            take_profit=take_profit,
            volume=self.config.trading.default_volume,  # Will be updated by risk manager
            confidence=market_state.ai_confidence,
            reasoning=reasoning,
            timestamp=datetime.now()
        )

    def _check_position_management(self, market_state: MarketState,
                                 positions: List[PositionInfo]) -> Optional[TradingSignal]:
        """Check if any positions should be closed"""
        for position in positions:
            # Check for partial profit taking first
            partial_close = self._check_partial_profit_taking(position, market_state)
            if partial_close:
                return partial_close

            # Check MACD trend reversal - this is the key improvement
            should_close_trend, trend_reason = self._check_macd_trend_reversal(position, market_state)
            if should_close_trend:
                return TradingSignal(
                    action='close',
                    strength=1.0,
                    entry_price=market_state.price,
                    stop_loss=0,
                    take_profit=0,
                    volume=position.volume,
                    confidence=1.0,
                    reasoning=f"Close position {position.ticket}: {trend_reason}",
                    timestamp=datetime.now()
                )

            # Check for profit protection
            profit_protection = self._check_profit_protection(position, market_state)
            if profit_protection:
                return profit_protection

            # Check regular risk management rules
            should_close, reason = self.risk_manager.should_close_position(
                position, market_state.price, market_state.atr_data.atr
            )

            if should_close:
                return TradingSignal(
                    action='close',
                    strength=1.0,
                    entry_price=market_state.price,
                    stop_loss=0,
                    take_profit=0,
                    volume=position.volume,
                    confidence=1.0,
                    reasoning=f"Close position {position.ticket}: {reason}",
                    timestamp=datetime.now()
                )

        return None

    def _check_macd_trend_reversal(self, position: PositionInfo, market_state: MarketState) -> tuple[bool, str]:
        """
        Check if position should be closed due to MACD trend reversal
        Close bearish orders when trend changes to bullish and vice versa
        """
        current_trend = market_state.macd_signal.trend
        crossover = market_state.macd_signal.crossover

        # For long positions (BUY orders)
        if position.type == 0:  # mt5.ORDER_TYPE_BUY
            # Close long position if MACD turns bearish
            if current_trend == 'bearish' or crossover == 'bearish_cross':
                return True, "MACD trend reversal: Bullish position closed due to bearish MACD signal"

        # For short positions (SELL orders)
        elif position.type == 1:  # mt5.ORDER_TYPE_SELL
            # Close short position if MACD turns bullish
            if current_trend == 'bullish' or crossover == 'bullish_cross':
                return True, "MACD trend reversal: Bearish position closed due to bullish MACD signal"

        return False, ""

    def _check_trend_alignment(self, market_state: MarketState) -> bool:
        """Check if signal aligns with overall market trend"""
        # Get longer-term trend from price action
        # This is a simplified implementation
        return True  # For now, allow all trends

    def _is_high_volatility_time(self) -> bool:
        """Check if current time is during high volatility periods"""
        from datetime import datetime
        current_hour = datetime.now().hour

        # Avoid trading during major news hours (simplified)
        # London open: 8-10 GMT, NY open: 13-15 GMT, overlap: 13-16 GMT
        high_volatility_hours = [8, 9, 13, 14, 15, 16]
        return current_hour in high_volatility_hours

    def _has_sufficient_confirmations(self, market_state: MarketState) -> bool:
        """Check if signal has sufficient confirmations"""
        confirmations = 0

        # MACD confirmation
        if market_state.macd_signal.crossover in ['bullish_cross', 'bearish_cross']:
            confirmations += 1

        # Trend confirmation
        if market_state.market_trend in ['bullish', 'bearish']:
            confirmations += 1

        # Pivot confirmation
        if market_state.pivot_points.current_level in ['above_pivot', 'below_pivot']:
            confirmations += 1

        # Require at least 2 confirmations
        return confirmations >= 2

    def _check_partial_profit_taking(self, position: PositionInfo, market_state: MarketState) -> Optional[TradingSignal]:
        """Check if we should take partial profits"""
        # Calculate current profit percentage
        if position.type == 0:  # Long position
            profit_pct = (market_state.price - position.price_open) / position.price_open
        else:  # Short position
            profit_pct = (position.price_open - market_state.price) / position.price_open

        # Take 50% profit when position is 1% profitable
        if profit_pct >= 0.01 and position.volume > 0.02:
            return TradingSignal(
                action='close',
                strength=1.0,
                entry_price=market_state.price,
                stop_loss=0,
                take_profit=0,
                volume=position.volume * 0.5,  # Close 50%
                confidence=1.0,
                reasoning=f"Partial profit taking: {profit_pct:.2%} profit",
                timestamp=datetime.now()
            )

        return None

    def _check_profit_protection(self, position: PositionInfo, market_state: MarketState) -> Optional[TradingSignal]:
        """Check if we should protect profits by closing position"""
        # Calculate current profit percentage
        if position.type == 0:  # Long position
            profit_pct = (market_state.price - position.price_open) / position.price_open
        else:  # Short position
            profit_pct = (position.price_open - market_state.price) / position.price_open

        # If position is profitable but showing signs of reversal, close it
        if profit_pct > 0.005:  # At least 0.5% profit
            # Check for reversal signals
            if market_state.macd_signal.trend != self._get_position_direction_trend(position):
                return TradingSignal(
                    action='close',
                    strength=1.0,
                    entry_price=market_state.price,
                    stop_loss=0,
                    take_profit=0,
                    volume=position.volume,
                    confidence=1.0,
                    reasoning=f"Profit protection: {profit_pct:.2%} profit, trend reversal detected",
                    timestamp=datetime.now()
                )

        return None

    def _get_position_direction_trend(self, position: PositionInfo) -> str:
        """Get the trend direction that matches the position"""
        if position.type == 0:  # Long position
            return 'bullish'
        else:  # Short position
            return 'bearish'

    def _determine_market_trend(self, data: pd.DataFrame,
                              macd_signal: MACDSignal,
                              pivot_points: PivotPoints) -> str:
        """Determine overall market trend"""
        # Simple trend determination based on price action and indicators
        recent_prices = data['close'].tail(20)
        if len(recent_prices) >= 2:
            price_trend = 'bullish' if recent_prices.iloc[-1] > recent_prices.iloc[0] else 'bearish'
        else:
            price_trend = 'sideways'

        # Combine with MACD and pivot analysis
        if (macd_signal.trend == 'bullish' and
            pivot_points.current_level == 'above_pivot' and
            price_trend == 'bullish'):
            return 'bullish'
        elif (macd_signal.trend == 'bearish' and
              pivot_points.current_level == 'below_pivot' and
              price_trend == 'bearish'):
            return 'bearish'
        else:
            return 'sideways'

    def _extract_features(self, data: pd.DataFrame) -> Optional[np.ndarray]:
        """Extract features for AI model"""
        if len(data) < 20:
            return None

        # Use recent price data normalized
        recent_data = data.tail(20)
        current_price = float(recent_data['close'].iloc[-1]) if len(recent_data) > 0 else 0

        # Normalize OHLC data
        features = []
        for col in ['open', 'high', 'low', 'close']:
            normalized = recent_data[col].values / current_price
            features.extend(normalized)

        # Ensure fixed feature size
        feature_array = np.array(features[:20])  # Take first 20 features
        if len(feature_array) < 20:
            feature_array = np.pad(feature_array, (0, 20 - len(feature_array)), 'constant')

        self.feature_cache = feature_array
        return feature_array.astype(np.float32)

    def _load_ai_model(self):
        """Load pre-trained AI model if available"""
        if self.ai_agent:
            model_path = self.config.ai.model_path if hasattr(self.config, 'ai') and hasattr(self.config.ai, 'model_path') else "models/dqn_gold_model.pth"
            self.ai_agent.load_model(model_path)

    def _create_empty_market_state(self) -> MarketState:
        """Create empty market state for error cases"""
        from ..indicators.technical_indicators import MACDSignal, ATRData, PivotPoints

        return MarketState(
            price=0, bid=0, ask=0, spread=0,
            macd_signal=MACDSignal(0, 0, 0, 'neutral', 'none'),
            atr_data=ATRData(0, 0, 'medium'),
            pivot_points=PivotPoints(0, 0, 0, 0, 0, 0, 0, 'at_pivot'),
            ai_prediction=None, ai_confidence=0,
            market_trend='sideways', volatility='medium'
        )

    def get_strategy_status(self) -> Dict:
        """Get current strategy status"""
        return {
            'last_signal': {
                'action': self.last_signal.action if self.last_signal else 'none',
                'strength': self.last_signal.strength if self.last_signal else 0,
                'timestamp': self.last_signal.timestamp.isoformat() if self.last_signal else None
            } if self.last_signal else None,
            'ai_enabled': self.config.enable_ai,
            'ai_model_loaded': self.ai_agent is not None,
            'min_signal_strength': self.min_signal_strength,
            'lookback_period': self.lookback_period
        }

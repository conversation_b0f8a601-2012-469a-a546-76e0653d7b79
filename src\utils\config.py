"""
Configuration management for MT5 Trading Bot
"""

import yaml
import os
from pathlib import Path
from typing import Dict, Any, Optional
from dataclasses import dataclass, asdict
import logging

logger = logging.getLogger(__name__)


@dataclass
class MT5Config:
    """MT5 connection configuration"""
    login: int
    password: str
    server: str
    symbol: str = "XAUUSD"
    magic_number: int = 234000


@dataclass
class TradingConfig:
    """Trading parameters configuration"""
    timeframe: int = 5  # 5-minute timeframe
    max_positions: int = 3
    max_daily_trades: int = 10
    risk_per_trade: float = 0.02  # 2% risk per trade
    min_volume: float = 0.01
    max_volume: float = 1.0
    volume_step: float = 0.01
    contract_size: int = 100  # Contract size for calculations
    default_volume: float = 0.01  # Default volume for signals
    require_confirmation: bool = False  # Require confirmation before trading
    min_time_between_trades: int = 30  # Minimum minutes between trades


@dataclass
class IndicatorConfig:
    """Technical indicators configuration"""
    macd_fast: int = 12
    macd_slow: int = 26
    macd_signal: int = 9
    atr_period: int = 14
    pivot_method: str = "standard"  # standard, fi<PERSON><PERSON><PERSON>, camarilla
    min_data_length: int = 50  # Minimum data length for indicator calculation
    min_data_length_atr: int = 20  # Minimum data length for ATR calculation
    lookback_period: int = 100  # Strategy lookback period
    trend_period: int = 20  # Period for trend calculation
    feature_size: int = 20  # AI feature vector size
    volatility_threshold: float = 1.5  # Threshold for volatility filter

@dataclass
class RiskConfig:
    """Risk management configuration"""
    max_drawdown: float = 0.10  # 10% maximum drawdown
    stop_loss_atr_multiplier: float = 2.0
    take_profit_atr_multiplier: float = 3.0
    trailing_stop: bool = True
    trailing_stop_distance: float = 50  # points
    max_spread: float = 30  # maximum spread in points
    margin_level_min: float = 200  # Minimum margin level percentage
    max_total_risk_exposure: float = 0.10  # Maximum 10% total risk exposure
    volatility_thresholds: Optional[Dict[str, float]] = None
    daily_loss_limit: float = 0.05  # Daily loss limit (5%)
    max_consecutive_losses: int = 3  # Maximum consecutive losses
    position_timeout_hours: int = 24  # Position timeout in hours
    volatility_adjustment: bool = False  # Adjust position size based on volatility
    min_profit_to_trail: float = 0.005  # Minimum profit to start trailing

    def __post_init__(self):
        if self.volatility_thresholds is None:
            self.volatility_thresholds = {
                'low': 0.5,
                'medium': 1.0,
                'high': 2.0
            }


@dataclass
class StrategyScoringConfig:
    """Strategy scoring configuration"""
    macd_crossover_bullish: float = 0.4
    macd_crossover_bearish: float = 0.4
    macd_trend_bullish: float = 0.2
    macd_trend_bearish: float = 0.2
    pivot_above: float = 0.2
    pivot_below: float = 0.2
    market_trend_bullish: float = 0.2
    market_trend_bearish: float = 0.2
    ai_prediction_weight: float = 0.5
    technical_weight: float = 0.7
    ai_weight: float = 0.3
    min_signal_strength: float = 0.6
    min_ai_confidence: float = 0.7
    volatility_filter: float = 0.7  # Multiplier for high volatility conditions
    trend_confirmation_required: bool = False  # Require trend confirmation
    min_crossover_strength: float = 0.3  # Minimum MACD crossover strength


@dataclass
class AIConfig:
    """AI/ML model configuration"""
    model_type: str = "dqn"  # dqn, ppo, a2c
    learning_rate: float = 0.001
    batch_size: int = 32
    memory_size: int = 10000
    epsilon_start: float = 1.0
    epsilon_end: float = 0.01
    epsilon_decay: float = 0.995
    target_update_frequency: int = 100
    training_frequency: int = 4


@dataclass
class BacktestConfig:
    """Backtesting configuration"""
    start_date: str = "2023-01-01"
    end_date: str = "2024-01-01"
    initial_balance: float = 1000.0
    commission: float = 0.0  # Commission per trade
    spread: float = 20  # Average spread in points
    data_count: int = 10000  # Number of bars to retrieve for backtest
    min_data_required: int = 1000  # Minimum data required for backtest
    progress_log_interval: int = 1000  # Log progress every N bars
    logging_level: str = "INFO"  # Logging level for backtest
    performance_thresholds: Optional[Dict[str, float]] = None

    def __post_init__(self):
        if self.performance_thresholds is None:
            self.performance_thresholds = {
                'win_rate_good': 0.5,
                'profit_factor_good': 1.5,
                'profit_factor_marginal': 1.0,
                'drawdown_low': 0.1,
                'drawdown_moderate': 0.2,
                'sharpe_good': 1.0,
                'sharpe_moderate': 0.5,
                'max_drawdown_warning': 0.15
            }


@dataclass
class DataConfig:
    """Data management configuration"""
    cache_size: int = 10000  # Number of bars to cache
    min_data_length: int = 100  # Minimum data length for analysis
    default_count: int = 1000  # Default number of bars to retrieve
    volatility_simulation: float = 0.002  # Volatility for mock data simulation
    price_range: Optional[Dict[str, float]] = None
    volume_range: Optional[Dict[str, int]] = None

    def __post_init__(self):
        if self.price_range is None:
            self.price_range = {'min': 1800, 'max': 2200}
        if self.volume_range is None:
            self.volume_range = {'min': 100, 'max': 1000}


@dataclass
class MockConfig:
    """Mock trading configuration"""
    initial_balance: float = 1000.0
    initial_equity: float = 1000.0
    base_price: float = 2000.0
    price_volatility: float = 0.001
    order_counter_start: int = 1000
    spread_default: int = 20
    volume_max: float = 100.0
    leverage: int = 100


@dataclass
class PerformanceConfig:
    """Performance analysis configuration"""
    annualization_factor: int = 252  # Trading days per year
    intraday_factor: int = 24  # Hours per day
    timeframe_factor: int = 12  # 5-minute intervals per hour
    var_confidence: float = 0.95  # VaR confidence level
    max_trades_for_metrics: int = 20  # Number of recent trades to show


@dataclass
class BotConfig:
    """Main bot configuration"""
    mt5: MT5Config
    trading: TradingConfig
    indicators: IndicatorConfig
    risk: RiskConfig
    strategy_scoring: StrategyScoringConfig
    ai: AIConfig
    backtest: BacktestConfig
    data: DataConfig
    mock: MockConfig
    performance: PerformanceConfig
    logging_level: str = "INFO"
    enable_ai: bool = True
    enable_backtesting: bool = False
    live_trading: bool = False


class ConfigManager:
    """Configuration manager for the trading bot"""

    def __init__(self, config_path: str = "config/config.yaml"):
        self.config_path = Path(config_path)
        self.config: Optional[BotConfig] = None

    def load_config(self) -> BotConfig:
        """Load configuration from YAML file"""
        try:
            if not self.config_path.exists():
                logger.warning(f"Config file {self.config_path} not found. Creating default config.")
                self.create_default_config()

            with open(self.config_path, 'r', encoding='utf-8') as file:
                config_data = yaml.safe_load(file)

            # Parse configuration sections
            mt5_config = MT5Config(**config_data.get('mt5', {}))
            trading_config = TradingConfig(**config_data.get('trading', {}))
            indicator_config = IndicatorConfig(**config_data.get('indicators', {}))
            risk_config = RiskConfig(**config_data.get('risk', {}))
            strategy_scoring_config = StrategyScoringConfig(**config_data.get('strategy', {}).get('scoring', {}))
            ai_config = AIConfig(**config_data.get('ai', {}))
            backtest_config = BacktestConfig(**config_data.get('backtest', {}))
            data_config = DataConfig(**config_data.get('data', {}))
            mock_config = MockConfig(**config_data.get('mock', {}))
            performance_config = PerformanceConfig(**config_data.get('performance', {}))

            self.config = BotConfig(
                mt5=mt5_config,
                trading=trading_config,
                indicators=indicator_config,
                risk=risk_config,
                strategy_scoring=strategy_scoring_config,
                ai=ai_config,
                backtest=backtest_config,
                data=data_config,
                mock=mock_config,
                performance=performance_config,
                logging_level=config_data.get('logging_level', 'INFO'),
                enable_ai=config_data.get('enable_ai', True),
                enable_backtesting=config_data.get('enable_backtesting', False),
                live_trading=config_data.get('live_trading', False)
            )

            logger.info(f"Configuration loaded from {self.config_path}")
            return self.config

        except Exception as e:
            logger.error(f"Error loading configuration: {e}")
            raise

    def save_config(self, config: BotConfig):
        """Save configuration to YAML file"""
        try:
            # Create config directory if it doesn't exist
            self.config_path.parent.mkdir(parents=True, exist_ok=True)

            # Convert dataclass to dictionary
            config_dict = {
                'mt5': asdict(config.mt5),
                'trading': asdict(config.trading),
                'indicators': asdict(config.indicators),
                'risk': asdict(config.risk),
                'strategy': {'scoring': asdict(config.strategy_scoring)},
                'ai': asdict(config.ai),
                'backtest': asdict(config.backtest),
                'data': asdict(config.data),
                'mock': asdict(config.mock),
                'performance': asdict(config.performance),
                'logging_level': config.logging_level,
                'enable_ai': config.enable_ai,
                'enable_backtesting': config.enable_backtesting,
                'live_trading': config.live_trading
            }

            with open(self.config_path, 'w', encoding='utf-8') as file:
                yaml.dump(config_dict, file, default_flow_style=False, indent=2)

            logger.info(f"Configuration saved to {self.config_path}")

        except Exception as e:
            logger.error(f"Error saving configuration: {e}")
            raise

    def create_default_config(self):
        """Create default configuration file"""
        default_config = BotConfig(
            mt5=MT5Config(
                login=0,  # User needs to set this
                password="",  # User needs to set this
                server="",  # User needs to set this
                symbol="XAUUSD",
                magic_number=234000
            ),
            trading=TradingConfig(),
            indicators=IndicatorConfig(),
            risk=RiskConfig(),
            strategy_scoring=StrategyScoringConfig(),
            ai=AIConfig(),
            backtest=BacktestConfig(),
            data=DataConfig(),
            mock=MockConfig(),
            performance=PerformanceConfig()
        )

        self.save_config(default_config)
        logger.info("Default configuration created")

    def get_config(self) -> BotConfig:
        """Get current configuration"""
        if self.config is None:
            self.config = self.load_config()
        return self.config

    def update_config(self, **kwargs):
        """Update configuration parameters"""
        if self.config is None:
            self.config = self.load_config()

        # Update configuration with new values
        for key, value in kwargs.items():
            if hasattr(self.config, key):
                setattr(self.config, key, value)
            else:
                logger.warning(f"Unknown configuration parameter: {key}")

        # Save updated configuration
        self.save_config(self.config)

    def validate_config(self) -> bool:
        """Validate configuration parameters"""
        if self.config is None:
            self.config = self.load_config()

        try:
            # Validate trading parameters
            if self.config.trading.risk_per_trade <= 0 or self.config.trading.risk_per_trade > 0.1:
                logger.error("Risk per trade should be between 0 and 0.1 (10%)")
                return False

            if self.config.trading.min_volume <= 0 or self.config.trading.max_volume <= 0:
                logger.error("Volume parameters should be positive")
                return False

            # Validate risk parameters
            if self.config.risk.max_drawdown <= 0 or self.config.risk.max_drawdown > 0.5:
                logger.error("Max drawdown should be between 0 and 0.5 (50%)")
                return False

            if self.config.risk.stop_loss_atr_multiplier <= 0:
                logger.error("Stop loss ATR multiplier should be positive")
                return False

            # Validate indicator parameters
            if self.config.indicators.macd_fast >= self.config.indicators.macd_slow:
                logger.error("MACD fast period should be less than slow period")
                return False

            if self.config.indicators.atr_period <= 0:
                logger.error("ATR period should be positive")
                return False

            logger.info("Configuration validation passed")
            return True

        except Exception as e:
            logger.error(f"Configuration validation failed: {e}")
            return False


def load_config(config_path: str = "config/config.yaml") -> BotConfig:
    """Load configuration from file"""
    config_manager = ConfigManager(config_path)
    return config_manager.load_config()


def get_env_config() -> Dict[str, Any]:
    """Get configuration from environment variables"""
    return {
        'mt5_login': os.getenv('MT5_LOGIN'),
        'mt5_password': os.getenv('MT5_PASSWORD'),
        'mt5_server': os.getenv('MT5_SERVER'),
        'live_trading': os.getenv('LIVE_TRADING', 'false').lower() == 'true'
    }

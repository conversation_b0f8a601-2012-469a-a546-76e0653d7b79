
ai:
  batch_size: 32
  epsilon_decay: 0.995
  epsilon_end: 0.01
  epsilon_start: 1.0
  learning_rate: 0.001
  memory_size: 10000
  model_type: dqn
  target_update_frequency: 100
  training_frequency: 4
backtest:
  # Thời gian backtest - <PERSON><PERSON> dàng thay đổi để test cá<PERSON> khoảng thời gian khác nhau
  start_date: '2025-06-11'    # <PERSON><PERSON><PERSON> bắt đầu backtest (YYYY-MM-DD)
  end_date: '2025-07-11'      # <PERSON><PERSON><PERSON> kết thúc backtest (YYYY-MM-DD)

  # Cấu hình tài chính
  initial_balance: 200        # Số dư ban đầu
  commission: 0.03            # Phí giao dịch
  spread: 200                 # Spread trung bình (points)

  # C<PERSON>u hình khác
  logging_level: ERROR        # Chỉ hiện lỗi nghiêm trọng, giảm spam log
enable_ai: true
enable_backtesting: true
indicators:
  atr_period: 14                  # <PERSON><PERSON><PERSON><PERSON> từ 21 xuống 14 để responsive hơn
  macd_fast: 12                   # T<PERSON><PERSON> từ 8 lên 12 để ít noise hơn
  macd_signal: 9                  # Tăng từ 5 lên 9 để ít false signal
  macd_slow: 26                   # Tăng từ 21 lên 26 để stable hơn
  pivot_method: standard

live_trading: true
logging_level: INFO
mt5:
  login: 126780412
  magic_number: 234000
  password: 'Exnessdz123@199'
  server: 'Exness-MT5Real7'
  symbol: XAUUSD
risk:
  max_drawdown: 0.15              # 15% max drawdown
  stop_loss_atr_multiplier: 2.5   # Conservative stop loss
  take_profit_atr_multiplier: 4.0 # Better risk-reward ratio
  daily_loss_limit: 0.03          # 3% daily loss limit
  position_timeout_hours: 12      # Shorter position holding
  max_spread: 150                 # Giảm spread tối đa để tránh giao dịch trong điều kiện xấu
  trailing_stop: true
  trailing_stop_distance: 30      # Giảm từ 50 xuống 30 để trailing stop sát hơn
  margin_level_min: 300           # Tăng từ 200 lên 300 để an toàn hơn
  max_total_risk_exposure: 1.0    # Giảm từ 150% xuống 100% để conservative hơn
  max_consecutive_losses: 3       # Dừng giao dịch sau 3 lệnh thua liên tiếp
strategy:
  scoring:
    macd_crossover_bullish: 0.5     # Tăng từ 0.4 lên 0.5 - crossover mạnh hơn
    macd_crossover_bearish: 0.5     # Tăng từ 0.4 lên 0.5 - crossover mạnh hơn
    macd_trend_bullish: 0.15        # Giảm từ 0.2 xuống 0.15 - trend ít quan trọng hơn crossover
    macd_trend_bearish: 0.15        # Giảm từ 0.2 xuống 0.15 - trend ít quan trọng hơn crossover
    pivot_above: 0.25               # Tăng từ 0.2 lên 0.25 - pivot quan trọng hơn
    pivot_below: 0.25               # Tăng từ 0.2 lên 0.25 - pivot quan trọng hơn
    market_trend_bullish: 0.15      # Giảm từ 0.2 xuống 0.15
    market_trend_bearish: 0.15      # Giảm từ 0.2 xuống 0.15
    ai_prediction_weight: 0.5
    technical_weight: 1.0
    ai_weight: 0.0
    min_signal_strength: 0.8        # Only strong signals
    min_ai_confidence: 0.75         # Tăng từ 0.7 lên 0.75
    volatility_filter: 0.7          # Thêm filter cho high volatility
trading:
  risk_per_trade: 0.008           # 0.8% risk per trade
  max_daily_trades: 6             # Reduced over-trading
  max_positions: 1
  max_volume: 0.5                 # Giảm từ 1.0 xuống 0.5 để conservative hơn
  min_volume: 0.01
  timeframe: 5
  volume_step: 0.01
  default_volume: 0.05            # Giảm từ 0.1 xuống 0.05
